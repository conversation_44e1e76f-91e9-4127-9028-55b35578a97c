// Cricket API Configuration
const API_CONFIG = {
  // Base Configuration
  BASE_URL: process.env.CRICKET_API_BASE_URL || 'https://api.cricket.com',
  API_VERSION: process.env.CRICKET_API_VERSION || 'v1',
  
  // Default Parameters
  DEFAULT_PARAMS: {
    lang: process.env.CRICKET_API_LANG || 'en',
    feed_format: process.env.CRICKET_API_FORMAT || 'json',
    client_id: process.env.CRICKET_API_CLIENT_ID || '',
    pagination: true,
    page_size: 20,
    page_number: 1
  },
  
  // Supported Values
  SUPPORTED_LANGUAGES: ['en', 'hi', 'es', 'fr'],
  SUPPORTED_FORMATS: ['json', 'xml'],
  SUPPORTED_VERSIONS: ['v1', 'v2'],
  
  // Pagination Limits
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 1,
  
  // API Endpoints
  ENDPOINTS: {
    LEAGUE: '/cricket/{version}/league',
    COUNTRY: '/cricket/{version}/country',
    SCHEDULE: '/cricket/{version}/schedule',
    GAME_SCORECARD: '/cricket/{version}/game/scorecard',
    GAME_HEAD_TO_HEAD: '/cricket/{version}/game/head-to-head',
    SERIES: '/cricket/{version}/series',
    RANKING: '/cricket/{version}/ranking',
    STATISTICS: '/cricket/{version}/stats'
  },
  
  // Helper Functions
  buildUrl: function(endpoint, params = {}) {
    const mergedParams = { ...this.DEFAULT_PARAMS, ...params };
    let url = this.BASE_URL + endpoint.replace('{version}', mergedParams.version || this.API_VERSION);
    
    const queryParams = new URLSearchParams();
    Object.keys(mergedParams).forEach(key => {
      if (mergedParams[key] !== undefined && mergedParams[key] !== null) {
        queryParams.append(key, mergedParams[key]);
      }
    });
    
    return url + '?' + queryParams.toString();
  },
  
  validateParams: function(params) {
    const errors = [];
    
    if (params.lang && !this.SUPPORTED_LANGUAGES.includes(params.lang)) {
      errors.push(`Unsupported language: ${params.lang}. Supported: ${this.SUPPORTED_LANGUAGES.join(', ')}`);
    }
    
    if (params.feed_format && !this.SUPPORTED_FORMATS.includes(params.feed_format)) {
      errors.push(`Unsupported format: ${params.feed_format}. Supported: ${this.SUPPORTED_FORMATS.join(', ')}`);
    }
    
    if (params.page_size && (params.page_size < this.MIN_PAGE_SIZE || params.page_size > this.MAX_PAGE_SIZE)) {
      errors.push(`Page size must be between ${this.MIN_PAGE_SIZE} and ${this.MAX_PAGE_SIZE}`);
    }
    
    if (!params.client_id) {
      errors.push('client_id is required');
    }
    
    return errors;
  }
};

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = API_CONFIG;
}

// Export for browser
if (typeof window !== 'undefined') {
  window.API_CONFIG = API_CONFIG;
}
