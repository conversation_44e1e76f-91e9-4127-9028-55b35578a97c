# 🏏 Cricket API Documentation

Professional Swagger UI documentation for Cricket API with global parameter management and environment configuration.

## 🚀 Features

- **Interactive API Documentation** - Complete Swagger UI interface
- **Global Parameter Management** - Set common parameters once, use everywhere
- **Environment Configuration** - Easy setup with .env files
- **JavaScript API Client** - Ready-to-use client library
- **Real-time API Testing** - Test endpoints directly from the documentation
- **Parameter Persistence** - Saves your settings in browser localStorage

## 📋 Common Parameters

These parameters are used across all API endpoints:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `version` | path | `v1` | API version |
| `lang` | query | `en` | Language code (en, hi, es, fr) |
| `feed_format` | query | `json` | Response format (json, xml) |
| `client_id` | query | - | **Required** - Your client identifier |
| `pagination` | query | `true` | Enable pagination |
| `page_size` | query | `20` | Items per page (1-100) |
| `page_number` | query | `1` | Page number |

## 🛠️ Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your values
CRICKET_API_CLIENT_ID=your_client_id_here
CRICKET_API_LANG=en
CRICKET_API_FORMAT=json
```

### 3. Start Documentation Server
```bash
npm start
# or for development with live reload
npm run dev
```

## 🎯 Usage

### Web Interface

1. **Open Documentation**: Navigate to `http://localhost:3000`
2. **Configure Parameters**: Click the "⚙️ Config" button
3. **Set Global Values**: Enter your `client_id` and preferences
4. **Test APIs**: Use "Try it out" on any endpoint

### JavaScript Client

```javascript
// Initialize client
const cricketAPI = new CricketAPIClient({
  client_id: 'your_client_id',
  lang: 'en',
  feed_format: 'json'
});

// Get leagues
const leagues = await cricketAPI.getLeagues();

// Get schedule with filters
const schedule = await cricketAPI.getSchedule({
  from_date: '2024-01-01',
  to_date: '2024-12-31',
  is_live: true
});

// Get game scorecard
const scorecard = await cricketAPI.getGameScorecard('game_123');
```

### Direct API Calls

```javascript
// Using fetch with global config
const response = await fetch(
  cricketAPI.buildUrl('/cricket/v1/league', {
    lang: 'hi',
    pagination: false
  })
);
```

## 📁 Project Structure

```
cricket-swagger/
├── public/
│   ├── index.html          # Swagger UI interface
│   ├── swagger.json        # API specification
│   └── js/
│       └── cricket-api-client.js  # JavaScript client
├── config/
│   └── api-config.js       # Configuration utilities
├── .env.example           # Environment template
├── package.json
└── README.md
```

## 🔧 Configuration Options

### Global Parameters in Swagger

The `swagger.json` now includes reusable parameter definitions:

```json
{
  "components": {
    "parameters": {
      "VersionParam": { "name": "version", "in": "path", "required": true },
      "LangParam": { "name": "lang", "in": "query", "default": "en" },
      "ClientIdParam": { "name": "client_id", "in": "query", "required": true }
    }
  }
}
```

### Environment Variables

```bash
# API Configuration
CRICKET_API_BASE_URL=https://api.cricket.com
CRICKET_API_VERSION=v1
CRICKET_API_CLIENT_ID=your_client_id
CRICKET_API_LANG=en
CRICKET_API_FORMAT=json

# Server Configuration
PORT=3000
NODE_ENV=development
```

## 🎨 UI Features

- **Configuration Panel**: Set global parameters with a floating panel
- **Parameter Persistence**: Settings saved in browser localStorage
- **Auto-injection**: Parameters automatically added to API requests
- **Validation**: Client-side parameter validation
- **Multiple Servers**: Support for dev, staging, and production environments

## 📚 API Endpoints

### Core Endpoints
- **Leagues**: `/cricket/{version}/league`
- **Countries**: `/cricket/{version}/country`
- **Schedule**: `/cricket/{version}/schedule`

### Game Data
- **Scorecard**: `/cricket/{version}/game/scorecard`
- **Head-to-Head**: `/cricket/{version}/game/head-to-head`
- **Commentary**: `/cricket/{version}/game/commentary`

### Series & Statistics
- **Series Info**: `/cricket/{version}/series`
- **Rankings**: `/cricket/{version}/ranking`
- **Statistics**: `/cricket/{version}/stats/*`

## 🔍 Tips

1. **Set Client ID First**: Always configure your client_id in the config panel
2. **Use Global Defaults**: Set common parameters once to avoid repetition
3. **Test Incrementally**: Start with simple endpoints like `/league`
4. **Check Browser Console**: Logs show API client initialization and errors
5. **Save Configurations**: Your settings persist across browser sessions

## 🚨 Troubleshooting

- **404 Errors**: Ensure server is running on correct port
- **Missing client_id**: Set it in the configuration panel
- **CORS Issues**: Check API server CORS settings
- **Parameter Errors**: Validate parameters using the built-in validation

## 📞 Support

For API access and support:
- Email: <EMAIL>
- Documentation: This Swagger UI interface
- Client Library: Use the included JavaScript client
