/**
 * Cricket API Client
 * Easy-to-use JavaScript client for Cricket API
 */
class CricketAPIClient {
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || 'https://api.cricket.com';
    this.version = config.version || 'v1';
    this.defaultParams = {
      lang: config.lang || 'en',
      feed_format: config.feed_format || 'json',
      client_id: config.client_id || '',
      ...config.defaultParams
    };
  }

  /**
   * Build URL with parameters
   */
  buildUrl(endpoint, params = {}) {
    const mergedParams = { ...this.defaultParams, ...params };
    let url = this.baseUrl + endpoint.replace('{version}', this.version);
    
    const queryParams = new URLSearchParams();
    Object.keys(mergedParams).forEach(key => {
      if (mergedParams[key] !== undefined && mergedParams[key] !== null && key !== 'version') {
        queryParams.append(key, mergedParams[key]);
      }
    });
    
    return url + '?' + queryParams.toString();
  }

  /**
   * Make API request
   */
  async request(endpoint, params = {}) {
    try {
      const url = this.buildUrl(endpoint, params);
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // API Methods
  async getLeagues(params = {}) {
    return this.request('/cricket/{version}/league', params);
  }

  async getCountries(params = {}) {
    return this.request('/cricket/{version}/country', params);
  }

  async getSchedule(params = {}) {
    return this.request('/cricket/{version}/schedule', params);
  }

  async getGameScorecard(gameId, params = {}) {
    return this.request('/cricket/{version}/game/scorecard', { game_id: gameId, ...params });
  }

  async getGameHeadToHead(gameId, params = {}) {
    return this.request('/cricket/{version}/game/head-to-head', { game_id: gameId, ...params });
  }

  async getSeriesSquad(seriesId, teamId, params = {}) {
    return this.request('/cricket/{version}/series/squad', { series_id: seriesId, team_id: teamId, ...params });
  }

  async getSeriesStanding(seriesId, params = {}) {
    return this.request('/cricket/{version}/series/standing', { series_id: seriesId, ...params });
  }

  async getRankings(params = {}) {
    return this.request('/cricket/{version}/ranking', params);
  }

  async getStatistics(params = {}) {
    return this.request('/cricket/{version}/stats/series', params);
  }
}

// Usage Examples
const examples = {
  // Initialize client
  init: () => {
    return new CricketAPIClient({
      client_id: 'your_client_id',
      lang: 'en',
      feed_format: 'json'
    });
  },

  // Get leagues
  getLeagues: async (client) => {
    try {
      const leagues = await client.getLeagues();
      console.log('Leagues:', leagues);
      return leagues;
    } catch (error) {
      console.error('Failed to get leagues:', error);
    }
  },

  // Get schedule with filters
  getSchedule: async (client) => {
    try {
      const schedule = await client.getSchedule({
        from_date: '2024-01-01',
        to_date: '2024-12-31',
        is_live: true
      });
      console.log('Schedule:', schedule);
      return schedule;
    } catch (error) {
      console.error('Failed to get schedule:', error);
    }
  }
};

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { CricketAPIClient, examples };
}

if (typeof window !== 'undefined') {
  window.CricketAPIClient = CricketAPIClient;
  window.CricketAPIExamples = examples;
}
