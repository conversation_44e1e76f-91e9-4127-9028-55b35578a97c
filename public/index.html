<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cricket API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.26.0/swagger-ui.css" />
  <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.26.0/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.26.0/favicon-16x16.png" sizes="16x16" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>

  <!-- API Configuration Panel -->
  <div id="api-config" style="position: fixed; top: 10px; right: 10px; background: white; padding: 15px; border: 1px solid #ccc; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000; max-width: 300px;">
    <h4 style="margin: 0 0 10px 0;">🏏 API Configuration</h4>
    <div style="margin-bottom: 8px;">
      <label style="display: block; font-size: 12px; margin-bottom: 2px;">Client ID:</label>
      <input type="text" id="client-id" placeholder="Enter your client ID" style="width: 100%; padding: 4px; font-size: 12px;">
    </div>
    <div style="margin-bottom: 8px;">
      <label style="display: block; font-size: 12px; margin-bottom: 2px;">Language:</label>
      <select id="language" style="width: 100%; padding: 4px; font-size: 12px;">
        <option value="en">English</option>
        <option value="hi">Hindi</option>
        <option value="es">Spanish</option>
        <option value="fr">French</option>
      </select>
    </div>
    <div style="margin-bottom: 8px;">
      <label style="display: block; font-size: 12px; margin-bottom: 2px;">Format:</label>
      <select id="format" style="width: 100%; padding: 4px; font-size: 12px;">
        <option value="json">JSON</option>
        <option value="xml">XML</option>
      </select>
    </div>
    <button onclick="updateSwaggerConfig()" style="width: 100%; padding: 6px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">Apply Settings</button>
    <button onclick="toggleConfig()" style="width: 100%; padding: 4px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 10px; margin-top: 5px;">Hide Panel</button>
  </div>

  <script src="./js/cricket-api-client.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.26.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.26.0/swagger-ui-standalone-preset.js"></script>
  <script>
    let swaggerUI;

    // Configuration functions
    function updateSwaggerConfig() {
      const clientId = document.getElementById('client-id').value;
      const language = document.getElementById('language').value;
      const format = document.getElementById('format').value;

      if (!clientId) {
        alert('Please enter a Client ID');
        return;
      }

      // Store in localStorage
      localStorage.setItem('cricket-api-config', JSON.stringify({
        client_id: clientId,
        lang: language,
        feed_format: format
      }));

      // Update Swagger UI with new defaults
      const newSpec = {
        ...window.swaggerSpec,
        servers: [
          {
            url: "https://api.cricket.com",
            description: "Production server",
            variables: {
              client_id: { default: clientId },
              lang: { default: language },
              feed_format: { default: format }
            }
          }
        ]
      };

      swaggerUI.specActions.updateSpec(JSON.stringify(newSpec));
      alert('Configuration updated! Default values will be used in API calls.');
    }

    function toggleConfig() {
      const panel = document.getElementById('api-config');
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    function loadSavedConfig() {
      const saved = localStorage.getItem('cricket-api-config');
      if (saved) {
        const config = JSON.parse(saved);
        document.getElementById('client-id').value = config.client_id || '';
        document.getElementById('language').value = config.lang || 'en';
        document.getElementById('format').value = config.feed_format || 'json';
      }
    }

    window.onload = function() {
      // Load saved configuration
      loadSavedConfig();

      // Begin Swagger UI call region
      swaggerUI = SwaggerUIBundle({
        url: './swagger.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        validatorUrl: "https://validator.swagger.io/validator",
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        requestInterceptor: function(request) {
          // Auto-add saved configuration to requests
          const saved = localStorage.getItem('cricket-api-config');
          if (saved) {
            const config = JSON.parse(saved);
            const url = new URL(request.url);

            // Add default parameters if not already present
            if (!url.searchParams.has('client_id') && config.client_id) {
              url.searchParams.set('client_id', config.client_id);
            }
            if (!url.searchParams.has('lang') && config.lang) {
              url.searchParams.set('lang', config.lang);
            }
            if (!url.searchParams.has('feed_format') && config.feed_format) {
              url.searchParams.set('feed_format', config.feed_format);
            }

            request.url = url.toString();
          }
          return request;
        },
        onComplete: function(swaggerApi) {
          console.log("🏏 Cricket API Documentation loaded successfully!");
          window.swaggerSpec = swaggerApi.spec;

          // Initialize API client
          const saved = localStorage.getItem('cricket-api-config');
          if (saved) {
            const config = JSON.parse(saved);
            window.cricketAPI = new CricketAPIClient(config);
            console.log("🚀 Cricket API Client initialized with saved config");
          }
        },
        onFailure: function(data) {
          console.error("Failed to load Swagger UI:", data);
        }
      });
      // End Swagger UI call region
    };

    // Add toggle button to show/hide config panel
    document.addEventListener('DOMContentLoaded', function() {
      const toggleBtn = document.createElement('button');
      toggleBtn.innerHTML = '⚙️ Config';
      toggleBtn.style.cssText = 'position: fixed; top: 10px; right: 320px; z-index: 1001; padding: 8px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;';
      toggleBtn.onclick = toggleConfig;
      document.body.appendChild(toggleBtn);
    });
  </script>
</body>
</html>
