{"openapi": "3.0.1", "info": {"title": "Cricket.API", "version": "v1"}, "paths": {"/cricket/{version}/league": {"get": {"tags": ["League"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/country": {"get": {"tags": ["Country"], "parameters": [{"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stat-lookup": {"get": {"tags": ["Lookup"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/schedule": {"get": {"tags": ["Schedule"], "parameters": [{"name": "gender", "in": "query", "schema": {"type": "string"}}, {"name": "league_ids", "in": "query", "schema": {"type": "string"}}, {"name": "parent_ids", "in": "query", "schema": {"type": "string"}}, {"name": "tour_ids", "in": "query", "schema": {"type": "string"}}, {"name": "competition_type_ids", "in": "query", "schema": {"type": "string"}}, {"name": "series_ids", "in": "query", "schema": {"type": "string"}}, {"name": "match_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_ids", "in": "query", "schema": {"type": "string"}}, {"name": "venue_ids", "in": "query", "schema": {"type": "string"}}, {"name": "country_ids", "in": "query", "schema": {"type": "string"}}, {"name": "parent_championship_ids", "in": "query", "schema": {"type": "string"}}, {"name": "championship_ids", "in": "query", "schema": {"type": "string"}}, {"name": "from_date", "in": "query", "schema": {"type": "string"}}, {"name": "to_date", "in": "query", "schema": {"type": "string"}}, {"name": "year", "in": "query", "schema": {"type": "string"}}, {"name": "is_deleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "timezone", "in": "query", "schema": {"type": "string", "default": "0000"}}, {"name": "is_live", "in": "query", "schema": {"type": "boolean"}}, {"name": "is_upcoming", "in": "query", "schema": {"type": "boolean"}}, {"name": "is_recent", "in": "query", "schema": {"type": "boolean"}}, {"name": "date_list", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "game_count", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "sorting", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HttpResponse"}}}}}}}, "/cricket/{version}/game/scorecard": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/head-to-head": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/batsman-split": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "inning", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/bowler-split": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "inning", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/team-performance": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "overs_split", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/player-performance": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "player_id", "in": "query", "schema": {"type": "string"}}, {"name": "overs_split", "in": "query", "schema": {"type": "string"}}, {"name": "ball_split", "in": "query", "schema": {"type": "string", "default": "1-10,11-20,21-30,31+"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/player-performance-additional": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "player_id", "in": "query", "schema": {"type": "string"}}, {"name": "stats_type", "in": "query", "schema": {"type": "string"}}, {"name": "data_type", "in": "query", "schema": {"type": "string"}}, {"name": "speed_split", "in": "query", "schema": {"type": "string", "default": "71-80,81-90,91-100,101-110,111-120,121-130,131-140,141-150,151-160"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/fielder-performance": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "stats_type", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/commentary": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "inning", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "all"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "key_event", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/over-by-over": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "inning", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/projection": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/blog": {"get": {"tags": ["Game"], "parameters": [{"name": "game_id", "in": "query", "schema": {"type": "string"}}, {"name": "asset_type_id", "in": "query", "schema": {"type": "string"}}, {"name": "session", "in": "query", "schema": {"type": "string"}}, {"name": "page_number", "in": "query", "schema": {"type": "string"}}, {"name": "page_size", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series/squad": {"get": {"tags": ["Series"], "parameters": [{"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series/standing": {"get": {"tags": ["Series"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series": {"get": {"tags": ["Series"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "league_id", "in": "query", "schema": {"type": "string"}}, {"name": "competition_type_ids", "in": "query", "schema": {"type": "string"}}, {"name": "year", "in": "query", "schema": {"type": "string"}}, {"name": "league_type_id", "in": "query", "schema": {"type": "string"}}, {"name": "championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "parent_championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "is_icc_event", "in": "query", "schema": {"type": "boolean"}}, {"name": "is_completed", "in": "query", "schema": {"type": "boolean"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series/mom": {"get": {"tags": ["Series"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series/tracker": {"get": {"tags": ["Series"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/team": {"get": {"tags": ["Team"], "parameters": [{"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "search_text", "in": "query", "schema": {"type": "string"}}, {"name": "gender", "in": "query", "schema": {"type": "string"}}, {"name": "member_status_id", "in": "query", "schema": {"type": "string"}}, {"name": "is_international", "in": "query", "schema": {"type": "boolean"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/team_list": {"get": {"tags": ["Team"], "parameters": [{"name": "comp_type_id", "in": "query", "schema": {"type": "string"}}, {"name": "parent_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/deleted_teams": {"get": {"tags": ["Team"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "team_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/player": {"get": {"tags": ["Player"], "parameters": [{"name": "player_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "is_international", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "nationality_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "search_text", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "series_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "parent_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "gender", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "skill_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/deleted_players": {"get": {"tags": ["Player"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "player_id", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/venue": {"get": {"tags": ["Venue"], "parameters": [{"name": "venue_id", "in": "query", "schema": {"type": "string"}}, {"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "country_id", "in": "query", "schema": {"type": "string"}}, {"name": "is_international", "in": "query", "schema": {"type": "boolean"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/championship_standing": {"get": {"tags": ["Championship"], "parameters": [{"name": "championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/championship": {"get": {"tags": ["Championship"], "parameters": [{"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "league_id", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "is_icc_event", "in": "query", "schema": {"type": "boolean"}}, {"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": true}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/championship/tracker": {"get": {"tags": ["Championship"], "parameters": [{"name": "championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/ranking": {"get": {"tags": ["Ranking"], "parameters": [{"name": "comp_type", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "date", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/historic_player_ranking": {"get": {"tags": ["Ranking"], "parameters": [{"name": "player_id", "in": "query", "schema": {"type": "string"}}, {"name": "comp_type", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "year", "in": "query", "schema": {"type": "string"}}, {"name": "month", "in": "query", "schema": {"type": "string"}}, {"name": "date", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/historic_team_ranking": {"get": {"tags": ["Ranking"], "parameters": [{"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "comp_type", "in": "query", "schema": {"type": "string"}}, {"name": "year", "in": "query", "schema": {"type": "string"}}, {"name": "month", "in": "query", "schema": {"type": "string"}}, {"name": "date", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/comp-type": {"get": {"tags": ["Comptype"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/predictor": {"post": {"tags": ["Standing"], "parameters": [{"name": "championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Prediction"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Prediction"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Prediction"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Prediction"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/standing/predictor": {"post": {"tags": ["Standing"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PredictionStanding"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PredictionStanding"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PredictionStanding"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PredictionStanding"}}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/series": {"get": {"tags": ["Statistics"], "parameters": [{"name": "series_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/parent": {"get": {"tags": ["Statistics"], "parameters": [{"name": "parent_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/comp-type": {"get": {"tags": ["Statistics"], "parameters": [{"name": "comptype_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/stats_list": {"get": {"tags": ["Statistics"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/player_level_stats": {"get": {"tags": ["Statistics"], "parameters": [{"name": "comp_type_id", "in": "query", "schema": {"type": "string"}}, {"name": "stat_id", "in": "query", "schema": {"type": "string"}}, {"name": "parent_id", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "team_id", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "year", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "top", "in": "query", "schema": {"type": "string", "default": "100"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/championship": {"get": {"tags": ["Statistics"], "parameters": [{"name": "championship_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/stats/parent_championship": {"get": {"tags": ["Statistics"], "parameters": [{"name": "parentchampionship_id", "in": "query", "schema": {"type": "string"}}, {"name": "team_id", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "0"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "version", "in": "path", "required": true, "schema": {"type": "string", "default": "v1"}}, {"name": "feed_format", "in": "query", "schema": {"type": "string", "default": "json"}}, {"name": "client_id", "in": "query", "schema": {"type": "string"}}, {"name": "pagination", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "page_number", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}}, "components": {"schemas": {"AddressFamily": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 28, 29, 65536, 65537, -1], "type": "integer", "format": "int32"}, "AppStatusCode": {"enum": [1, -6, -5, -4, -3, -2, -1], "type": "integer", "format": "int32"}, "AsnEncodedData": {"type": "object", "properties": {"oid": {"$ref": "#/components/schemas/Oid"}, "rawData": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false}, "Assembly": {"type": "object", "properties": {"definedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "exportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "codeBase": {"type": "string", "nullable": true, "readOnly": true}, "entryPoint": {"$ref": "#/components/schemas/MethodInfo"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "imageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "isDynamic": {"type": "boolean", "readOnly": true}, "location": {"type": "string", "nullable": true, "readOnly": true}, "reflectionOnly": {"type": "boolean", "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "isFullyTrusted": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "escapedCodeBase": {"type": "string", "nullable": true, "readOnly": true}, "manifestModule": {"$ref": "#/components/schemas/Module"}, "modules": {"type": "array", "items": {"$ref": "#/components/schemas/Module"}, "nullable": true, "readOnly": true}, "globalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "hostContext": {"type": "integer", "format": "int64", "readOnly": true}, "securityRuleSet": {"$ref": "#/components/schemas/SecurityRuleSet"}}, "additionalProperties": false}, "AsymmetricAlgorithm": {"type": "object", "properties": {"keySize": {"type": "integer", "format": "int32"}, "legalKeySizes": {"type": "array", "items": {"$ref": "#/components/schemas/KeySizes"}, "nullable": true, "readOnly": true}, "signatureAlgorithm": {"type": "string", "nullable": true, "readOnly": true}, "keyExchangeAlgorithm": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CallingConventions": {"enum": [1, 2, 3, 32, 64], "type": "integer", "format": "int32"}, "CancellationToken": {"type": "object", "properties": {"isCancellationRequested": {"type": "boolean", "readOnly": true}, "canBeCanceled": {"type": "boolean", "readOnly": true}, "waitHandle": {"$ref": "#/components/schemas/WaitHandle"}}, "additionalProperties": false}, "Champioshipteam": {"type": "object", "properties": {"team_id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "is_win": {"type": "boolean"}, "is_loss": {"type": "boolean"}, "is_tied": {"type": "boolean"}, "is_draw": {"type": "boolean"}, "is_batted_first": {"type": "boolean"}, "is_super_over": {"type": "boolean"}, "over": {"type": "number", "format": "double"}, "runs": {"type": "integer", "format": "int32"}, "is_no_result": {"type": "boolean"}}, "additionalProperties": false}, "Claim": {"type": "object", "properties": {"issuer": {"type": "string", "nullable": true, "readOnly": true}, "originalIssuer": {"type": "string", "nullable": true, "readOnly": true}, "properties": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true, "readOnly": true}, "subject": {"$ref": "#/components/schemas/ClaimsIdentity"}, "type": {"type": "string", "nullable": true, "readOnly": true}, "value": {"type": "string", "nullable": true, "readOnly": true}, "valueType": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ClaimsIdentity": {"type": "object", "properties": {"authenticationType": {"type": "string", "nullable": true, "readOnly": true}, "isAuthenticated": {"type": "boolean", "readOnly": true}, "actor": {"$ref": "#/components/schemas/ClaimsIdentity"}, "bootstrapContext": {"nullable": true}, "claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "nullable": true, "readOnly": true}, "label": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "nameClaimType": {"type": "string", "nullable": true, "readOnly": true}, "roleClaimType": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ClaimsPrincipal": {"type": "object", "properties": {"claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "nullable": true, "readOnly": true}, "identities": {"type": "array", "items": {"$ref": "#/components/schemas/ClaimsIdentity"}, "nullable": true, "readOnly": true}, "identity": {"$ref": "#/components/schemas/IIdentity"}}, "additionalProperties": false}, "ConnectionInfo": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "remoteIpAddress": {"$ref": "#/components/schemas/IPAddress"}, "remotePort": {"type": "integer", "format": "int32"}, "localIpAddress": {"$ref": "#/components/schemas/IPAddress"}, "localPort": {"type": "integer", "format": "int32"}, "clientCertificate": {"$ref": "#/components/schemas/X509Certificate2"}}, "additionalProperties": false}, "ConstructorInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}}, "additionalProperties": false}, "CustomAttributeData": {"type": "object", "properties": {"attributeType": {"$ref": "#/components/schemas/Type"}, "constructor": {"$ref": "#/components/schemas/ConstructorInfo"}, "constructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "nullable": true, "readOnly": true}, "namedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeNamedArgument"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CustomAttributeNamedArgument": {"type": "object", "properties": {"memberInfo": {"$ref": "#/components/schemas/MemberInfo"}, "typedValue": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "memberName": {"type": "string", "nullable": true, "readOnly": true}, "isField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CustomAttributeTypedArgument": {"type": "object", "properties": {"argumentType": {"$ref": "#/components/schemas/Type"}, "value": {"nullable": true}}, "additionalProperties": false}, "EventAttributes": {"enum": [0, 512, 1024], "type": "integer", "format": "int32"}, "EventInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "attributes": {"$ref": "#/components/schemas/EventAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "addMethod": {"$ref": "#/components/schemas/MethodInfo"}, "removeMethod": {"$ref": "#/components/schemas/MethodInfo"}, "raiseMethod": {"$ref": "#/components/schemas/MethodInfo"}, "isMulticast": {"type": "boolean", "readOnly": true}, "eventHandlerType": {"$ref": "#/components/schemas/Type"}}, "additionalProperties": false}, "FeedTime": {"type": "object", "properties": {"utc_time": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FieldAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 16, 32, 64, 128, 256, 512, 1024, 4096, 8192, 32768, 38144], "type": "integer", "format": "int32"}, "FieldInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "attributes": {"$ref": "#/components/schemas/FieldAttributes"}, "fieldType": {"$ref": "#/components/schemas/Type"}, "isInitOnly": {"type": "boolean", "readOnly": true}, "isLiteral": {"type": "boolean", "readOnly": true}, "isNotSerialized": {"type": "boolean", "readOnly": true}, "isPinvokeImpl": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "fieldHandle": {"$ref": "#/components/schemas/RuntimeFieldHandle"}}, "additionalProperties": false}, "GenericParameterAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 28], "type": "integer", "format": "int32"}, "HTTPMeta": {"type": "object", "properties": {"_debugstacktrace": {"type": "boolean"}, "_pagination": {"type": "boolean"}, "app_status_code": {"$ref": "#/components/schemas/AppStatusCode"}, "message": {"type": "string", "nullable": true}, "status_code": {"$ref": "#/components/schemas/HttpStatusCode"}, "exception_message": {"type": "string", "nullable": true}, "pagination": {"type": "boolean"}, "page_number": {"type": "integer", "format": "int64"}, "page_size": {"type": "integer", "format": "int64"}, "count": {"type": "integer", "format": "int64"}, "timestamp": {"$ref": "#/components/schemas/FeedTime"}}, "additionalProperties": false}, "HTTPResponse": {"type": "object", "properties": {"data": {"nullable": true}, "meta": {"$ref": "#/components/schemas/HTTPMeta"}}, "additionalProperties": false}, "HostString": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "hasValue": {"type": "boolean", "readOnly": true}, "host": {"type": "string", "nullable": true, "readOnly": true}, "port": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "HttpContext": {"type": "object", "properties": {"features": {"type": "array", "items": {"$ref": "#/components/schemas/TypeObjectKeyValuePair"}, "nullable": true, "readOnly": true}, "request": {"$ref": "#/components/schemas/HttpRequest"}, "response": {"$ref": "#/components/schemas/HttpResponse"}, "connection": {"$ref": "#/components/schemas/ConnectionInfo"}, "webSockets": {"$ref": "#/components/schemas/WebSocketManager"}, "user": {"$ref": "#/components/schemas/ClaimsPrincipal"}, "items": {"type": "object", "additionalProperties": {}, "nullable": true}, "requestServices": {"$ref": "#/components/schemas/IServiceProvider"}, "requestAborted": {"$ref": "#/components/schemas/CancellationToken"}, "traceIdentifier": {"type": "string", "nullable": true}, "session": {"$ref": "#/components/schemas/ISession"}}, "additionalProperties": false}, "HttpRequest": {"type": "object", "properties": {"httpContext": {"$ref": "#/components/schemas/HttpContext"}, "method": {"type": "string", "nullable": true}, "scheme": {"type": "string", "nullable": true}, "isHttps": {"type": "boolean"}, "host": {"$ref": "#/components/schemas/HostString"}, "pathBase": {"$ref": "#/components/schemas/PathString"}, "path": {"$ref": "#/components/schemas/PathString"}, "queryString": {"$ref": "#/components/schemas/QueryString"}, "query": {"type": "array", "items": {"$ref": "#/components/schemas/StringStringValuesKeyValuePair"}, "nullable": true}, "protocol": {"type": "string", "nullable": true}, "headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true, "readOnly": true}, "cookies": {"type": "array", "items": {"$ref": "#/components/schemas/StringStringKeyValuePair"}, "nullable": true}, "contentLength": {"type": "integer", "format": "int64", "nullable": true}, "contentType": {"type": "string", "nullable": true}, "body": {"$ref": "#/components/schemas/Stream"}, "bodyReader": {"$ref": "#/components/schemas/PipeReader"}, "hasFormContentType": {"type": "boolean", "readOnly": true}, "form": {"type": "array", "items": {"$ref": "#/components/schemas/StringStringValuesKeyValuePair"}, "nullable": true}, "routeValues": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "HttpResponse": {"type": "object", "properties": {"httpContext": {"$ref": "#/components/schemas/HttpContext"}, "statusCode": {"type": "integer", "format": "int32"}, "headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "nullable": true, "readOnly": true}, "body": {"$ref": "#/components/schemas/Stream"}, "bodyWriter": {"$ref": "#/components/schemas/PipeWriter"}, "contentLength": {"type": "integer", "format": "int64", "nullable": true}, "contentType": {"type": "string", "nullable": true}, "cookies": {"$ref": "#/components/schemas/IResponseCookies"}, "hasStarted": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "HttpStatusCode": {"enum": [100, 101, 102, 103, 200, 201, 202, 203, 204, 205, 206, 207, 208, 226, 300, 301, 302, 303, 304, 305, 306, 307, 308, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511], "type": "integer", "format": "int32"}, "ICustomAttributeProvider": {"type": "object", "additionalProperties": false}, "IIdentity": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "authenticationType": {"type": "string", "nullable": true, "readOnly": true}, "isAuthenticated": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "IPAddress": {"type": "object", "properties": {"addressFamily": {"$ref": "#/components/schemas/AddressFamily"}, "scopeId": {"type": "integer", "format": "int64"}, "isIPv6Multicast": {"type": "boolean", "readOnly": true}, "isIPv6LinkLocal": {"type": "boolean", "readOnly": true}, "isIPv6SiteLocal": {"type": "boolean", "readOnly": true}, "isIPv6Teredo": {"type": "boolean", "readOnly": true}, "isIPv6UniqueLocal": {"type": "boolean", "readOnly": true}, "isIPv4MappedToIPv6": {"type": "boolean", "readOnly": true}, "address": {"type": "integer", "format": "int64", "deprecated": true}}, "additionalProperties": false}, "IResponseCookies": {"type": "object", "additionalProperties": false}, "IServiceProvider": {"type": "object", "additionalProperties": false}, "ISession": {"type": "object", "properties": {"isAvailable": {"type": "boolean", "readOnly": true}, "id": {"type": "string", "nullable": true, "readOnly": true}, "keys": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "IntPtr": {"type": "object", "additionalProperties": false}, "KeySizes": {"type": "object", "properties": {"minSize": {"type": "integer", "format": "int32"}, "maxSize": {"type": "integer", "format": "int32"}, "skipSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LayoutKind": {"enum": [0, 2, 3], "type": "integer", "format": "int32"}, "MemberInfo": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/MemberTypes"}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "MemberTypes": {"enum": [1, 2, 4, 8, 16, 32, 64, 128, 191], "type": "integer", "format": "int32"}, "MethodAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 53248], "type": "integer", "format": "int32"}, "MethodBase": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/MemberTypes"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MethodImplAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 4096, 65535], "type": "integer", "format": "int32"}, "MethodInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "returnParameter": {"$ref": "#/components/schemas/ParameterInfo"}, "returnType": {"$ref": "#/components/schemas/Type"}, "returnTypeCustomAttributes": {"$ref": "#/components/schemas/ICustomAttributeProvider"}}, "additionalProperties": false}, "Module": {"type": "object", "properties": {"assembly": {"$ref": "#/components/schemas/Assembly"}, "fullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "moduleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "scopeName": {"type": "string", "nullable": true, "readOnly": true}, "moduleHandle": {"$ref": "#/components/schemas/ModuleHandle"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ModuleHandle": {"type": "object", "properties": {"mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "Oid": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "friendlyName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ParameterAttributes": {"enum": [0, 1, 2, 4, 8, 16, 4096, 8192, 16384, 32768, 61440], "type": "integer", "format": "int32"}, "ParameterInfo": {"type": "object", "properties": {"attributes": {"$ref": "#/components/schemas/ParameterAttributes"}, "member": {"$ref": "#/components/schemas/MemberInfo"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "parameterType": {"$ref": "#/components/schemas/Type"}, "position": {"type": "integer", "format": "int32", "readOnly": true}, "isIn": {"type": "boolean", "readOnly": true}, "isLcid": {"type": "boolean", "readOnly": true}, "isOptional": {"type": "boolean", "readOnly": true}, "isOut": {"type": "boolean", "readOnly": true}, "isRetval": {"type": "boolean", "readOnly": true}, "defaultValue": {"nullable": true, "readOnly": true}, "rawDefaultValue": {"nullable": true, "readOnly": true}, "hasDefaultValue": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PathString": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "hasValue": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PipeReader": {"type": "object", "additionalProperties": false}, "PipeWriter": {"type": "object", "properties": {"canGetUnflushedBytes": {"type": "boolean", "readOnly": true}, "unflushedBytes": {"type": "integer", "format": "int64", "readOnly": true}}, "additionalProperties": false}, "Prediction": {"type": "object", "properties": {"team_id": {"type": "integer", "format": "int32"}, "team_name": {"type": "string", "nullable": true}, "team_short_name": {"type": "string", "nullable": true}, "match_played": {"type": "integer", "format": "int32", "nullable": true}, "wins": {"type": "integer", "format": "int32", "nullable": true}, "loss": {"type": "integer", "format": "int32", "nullable": true}, "tie": {"type": "integer", "format": "int32", "nullable": true}, "drawn": {"type": "integer", "format": "int32", "nullable": true}, "penalty": {"type": "integer", "format": "int32", "nullable": true}, "pct": {"type": "integer", "format": "int32", "nullable": true}, "total_points": {"type": "integer", "format": "int32", "nullable": true}, "pct_percentage": {"type": "number", "format": "double", "nullable": true}, "rank": {"type": "integer", "format": "int32", "nullable": true}, "change": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PredictionStanding": {"type": "object", "properties": {"stage_name": {"type": "string", "nullable": true}, "match_id": {"type": "integer", "format": "int32"}, "championship_team": {"type": "array", "items": {"$ref": "#/components/schemas/Champioshipteam"}, "nullable": true}}, "additionalProperties": false}, "PropertyAttributes": {"enum": [0, 512, 1024, 4096, 8192, 16384, 32768, 62464], "type": "integer", "format": "int32"}, "PropertyInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "module": {"$ref": "#/components/schemas/Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "propertyType": {"$ref": "#/components/schemas/Type"}, "attributes": {"$ref": "#/components/schemas/PropertyAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "canRead": {"type": "boolean", "readOnly": true}, "canWrite": {"type": "boolean", "readOnly": true}, "getMethod": {"$ref": "#/components/schemas/MethodInfo"}, "setMethod": {"$ref": "#/components/schemas/MethodInfo"}}, "additionalProperties": false}, "PublicKey": {"type": "object", "properties": {"encodedKeyValue": {"$ref": "#/components/schemas/AsnEncodedData"}, "encodedParameters": {"$ref": "#/components/schemas/AsnEncodedData"}, "key": {"$ref": "#/components/schemas/AsymmetricAlgorithm"}, "oid": {"$ref": "#/components/schemas/Oid"}}, "additionalProperties": false}, "QueryString": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "hasValue": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "RuntimeFieldHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeMethodHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeTypeHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "SafeWaitHandle": {"type": "object", "properties": {"isClosed": {"type": "boolean", "readOnly": true}, "isInvalid": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "SecurityRuleSet": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Stream": {"type": "object", "properties": {"canRead": {"type": "boolean", "readOnly": true}, "canWrite": {"type": "boolean", "readOnly": true}, "canSeek": {"type": "boolean", "readOnly": true}, "canTimeout": {"type": "boolean", "readOnly": true}, "length": {"type": "integer", "format": "int64", "readOnly": true}, "position": {"type": "integer", "format": "int64"}, "readTimeout": {"type": "integer", "format": "int32"}, "writeTimeout": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StringStringKeyValuePair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringStringValuesKeyValuePair": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "StructLayoutAttribute": {"type": "object", "properties": {"typeId": {"nullable": true, "readOnly": true}, "value": {"$ref": "#/components/schemas/LayoutKind"}}, "additionalProperties": false}, "Type": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/Assembly"}, "module": {"$ref": "#/components/schemas/Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "declaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/Type"}, "isSerializable": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TypeAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 24, 32, 128, 256, 1024, 2048, 4096, 8192, 16384, 65536, 131072, 196608, 262144, 264192, 1048576, 12582912], "type": "integer", "format": "int32"}, "TypeInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/Assembly"}, "module": {"$ref": "#/components/schemas/Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/Type"}, "declaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/Type"}, "isSerializable": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}, "genericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "declaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/ConstructorInfo"}, "nullable": true, "readOnly": true}, "declaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/EventInfo"}, "nullable": true, "readOnly": true}, "declaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}, "nullable": true, "readOnly": true}, "declaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/MemberInfo"}, "nullable": true, "readOnly": true}, "declaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/MethodInfo"}, "nullable": true, "readOnly": true}, "declaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "declaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyInfo"}, "nullable": true, "readOnly": true}, "implementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TypeObjectKeyValuePair": {"type": "object", "properties": {"key": {"$ref": "#/components/schemas/Type"}, "value": {"nullable": true}}, "additionalProperties": false}, "WaitHandle": {"type": "object", "properties": {"handle": {"$ref": "#/components/schemas/IntPtr"}, "safeWaitHandle": {"$ref": "#/components/schemas/SafeWaitHandle"}}, "additionalProperties": false}, "WebSocketManager": {"type": "object", "properties": {"isWebSocketRequest": {"type": "boolean", "readOnly": true}, "webSocketRequestedProtocols": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "X500DistinguishedName": {"type": "object", "properties": {"oid": {"$ref": "#/components/schemas/Oid"}, "rawData": {"type": "string", "format": "byte", "nullable": true}, "name": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "X509Certificate2": {"type": "object", "properties": {"handle": {"$ref": "#/components/schemas/IntPtr"}, "issuer": {"type": "string", "nullable": true, "readOnly": true}, "subject": {"type": "string", "nullable": true, "readOnly": true}, "archived": {"type": "boolean"}, "extensions": {"type": "array", "items": {"$ref": "#/components/schemas/X509Extension"}, "nullable": true, "readOnly": true}, "friendlyName": {"type": "string", "nullable": true}, "hasPrivateKey": {"type": "boolean", "readOnly": true}, "privateKey": {"$ref": "#/components/schemas/AsymmetricAlgorithm"}, "issuerName": {"$ref": "#/components/schemas/X500DistinguishedName"}, "notAfter": {"type": "string", "format": "date-time", "readOnly": true}, "notBefore": {"type": "string", "format": "date-time", "readOnly": true}, "publicKey": {"$ref": "#/components/schemas/PublicKey"}, "rawData": {"type": "string", "format": "byte", "nullable": true, "readOnly": true}, "serialNumber": {"type": "string", "nullable": true, "readOnly": true}, "signatureAlgorithm": {"$ref": "#/components/schemas/Oid"}, "subjectName": {"$ref": "#/components/schemas/X500DistinguishedName"}, "thumbprint": {"type": "string", "nullable": true, "readOnly": true}, "version": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "X509Extension": {"type": "object", "properties": {"oid": {"$ref": "#/components/schemas/Oid"}, "rawData": {"type": "string", "format": "byte", "nullable": true}, "critical": {"type": "boolean"}}, "additionalProperties": false}}}}