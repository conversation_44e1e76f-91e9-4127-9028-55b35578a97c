{"openapi": "3.0.1", "info": {"title": "Cricket API", "version": "1.0.0", "description": "Comprehensive Cricket API for accessing cricket data including leagues, matches, players, and statistics.", "contact": {"name": "Cricket API Support", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.cricket.com", "description": "Production server"}, {"url": "https://staging-api.cricket.com", "description": "Staging server"}, {"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/cricket/{version}/league": {"get": {"tags": ["League"], "summary": "Get cricket leagues", "description": "Retrieve list of cricket leagues", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/country": {"get": {"tags": ["Country"], "summary": "Get countries", "description": "Retrieve list of countries", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"$ref": "#/components/parameters/PaginationParam"}, {"$ref": "#/components/parameters/PageSizeParam"}, {"$ref": "#/components/parameters/PageNumberParam"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/schedule": {"get": {"tags": ["Schedule"], "summary": "Get match schedule", "description": "Retrieve cricket match schedule", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"name": "from_date", "in": "query", "description": "Start date (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "to_date", "in": "query", "description": "End date (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "is_live", "in": "query", "description": "Filter live matches", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/scorecard": {"get": {"tags": ["Game"], "summary": "Get game scorecard", "description": "Retrieve detailed scorecard for a specific game", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"name": "game_id", "in": "query", "required": true, "description": "Game identifier", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/game/head-to-head": {"get": {"tags": ["Game"], "summary": "Get head-to-head statistics", "description": "Retrieve head-to-head statistics between teams", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"name": "game_id", "in": "query", "required": true, "description": "Game identifier", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/series": {"get": {"tags": ["Series"], "summary": "Get cricket series", "description": "Retrieve cricket series information", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"name": "series_id", "in": "query", "description": "Series identifier", "schema": {"type": "string"}}, {"name": "year", "in": "query", "description": "Year filter", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}, "/cricket/{version}/ranking": {"get": {"tags": ["Ranking"], "summary": "Get team/player rankings", "description": "Retrieve ICC rankings for teams and players", "parameters": [{"$ref": "#/components/parameters/VersionParam"}, {"$ref": "#/components/parameters/LangParam"}, {"$ref": "#/components/parameters/FeedFormatParam"}, {"$ref": "#/components/parameters/ClientIdParam"}, {"name": "comp_type", "in": "query", "description": "Competition type (test, odi, t20)", "schema": {"type": "string", "enum": ["test", "odi", "t20"]}}, {"name": "type", "in": "query", "description": "Ranking type (team, batting, bowling, allrounder)", "schema": {"type": "string", "enum": ["team", "batting", "bowling", "allrounder"]}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPResponse"}}}}}}}}, "components": {"parameters": {"VersionParam": {"name": "version", "in": "path", "required": true, "description": "API version", "schema": {"type": "string", "default": "v1", "enum": ["v1", "v2"]}}, "LangParam": {"name": "lang", "in": "query", "description": "Language code for localized content", "schema": {"type": "string", "default": "en", "enum": ["en", "hi", "es", "fr"]}}, "FeedFormatParam": {"name": "feed_format", "in": "query", "description": "Response format", "schema": {"type": "string", "default": "json", "enum": ["json", "xml"]}}, "ClientIdParam": {"name": "client_id", "in": "query", "description": "Client identifier for API access", "required": true, "schema": {"type": "string", "minLength": 1}}, "PaginationParam": {"name": "pagination", "in": "query", "description": "Enable pagination", "schema": {"type": "boolean", "default": true}}, "PageSizeParam": {"name": "page_size", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "format": "int32", "default": 20, "minimum": 1, "maximum": 100}}, "PageNumberParam": {"name": "page_number", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1, "minimum": 1}}}, "schemas": {"HTTPResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Response status"}, "data": {"type": "object", "description": "Response data"}, "message": {"type": "string", "description": "Response message"}}}}}}